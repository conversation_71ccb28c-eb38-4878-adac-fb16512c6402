# 材质调节功能使用示例

## 基础使用流程

### 1. 选择可编辑材质
1. 在渲染页面加载3D模型
2. 在右侧属性面板查看可编辑材质列表
3. 点击材质球激活要编辑的材质
4. 激活的材质会在3D视图中高亮显示

### 2. 切换到自定义材质编辑
1. 点击"自定义"标签页
2. 确保已选择可编辑材质
3. 开始调节各项材质参数

## 发光效果使用示例

### 创建蓝色发光效果
```typescript
// 材质设置示例
const glowMaterial = {
  color: '#ffffff',
  metalness: 0.1,
  roughness: 0.2,
  opacity: 1.0,
  emissive: '#00BFFF',        // 蓝色发光
  emissiveIntensity: 0.8,     // 发光强度
  proceduralTexture: {
    type: 'none',
    scale: 1,
    intensity: 0.5,
    color1: '#ffffff',
    color2: '#000000'
  }
};
```

### 使用发光预设
1. 在发光预设区域选择预设颜色
2. 系统自动应用对应的发光颜色和强度
3. 可以进一步调节发光强度滑块

### 自定义发光颜色
1. 点击发光颜色预览框
2. 使用颜色选择器选择自定义颜色
3. 调节发光强度滑块控制亮度

## 程序化纹理使用示例

### 创建木纹效果
```typescript
const woodMaterial = {
  color: '#8B4513',           // 棕色基础色
  metalness: 0.0,             // 非金属
  roughness: 0.8,             // 较高粗糙度
  opacity: 1.0,
  emissive: '#000000',
  emissiveIntensity: 0,
  proceduralTexture: {
    type: 'wood',             // 木纹类型
    scale: 1.5,               // 纹理缩放
    intensity: 0.7,           // 纹理强度
    color1: '#8B4513',        // 深棕色
    color2: '#D2691E'         // 浅棕色
  }
};
```

### 创建大理石效果
```typescript
const marbleMaterial = {
  color: '#F5F5DC',           // 米白色基础
  metalness: 0.1,             // 轻微金属感
  roughness: 0.3,             // 中等粗糙度
  opacity: 1.0,
  emissive: '#000000',
  emissiveIntensity: 0,
  proceduralTexture: {
    type: 'marble',           // 大理石类型
    scale: 0.8,               // 较小缩放
    intensity: 0.6,           // 中等强度
    color1: '#F5F5DC',        // 浅色纹理
    color2: '#696969'         // 深色纹理
  }
};
```

### 创建斑点纹理效果
```typescript
const spotsMaterial = {
  color: '#FF6347',           // 番茄红基础色
  metalness: 0.2,
  roughness: 0.6,
  opacity: 1.0,
  emissive: '#000000',
  emissiveIntensity: 0,
  proceduralTexture: {
    type: 'spots',            // 斑点类型
    scale: 2.0,               // 较大缩放
    intensity: 0.8,           // 高强度
    color1: '#FF6347',        // 红色斑点
    color2: '#8B0000'         // 深红色背景
  }
};
```

## 组合效果示例

### 发光木纹材质
```typescript
const glowingWood = {
  color: '#8B4513',
  metalness: 0.0,
  roughness: 0.7,
  opacity: 1.0,
  emissive: '#FFD700',        // 金色发光
  emissiveIntensity: 0.3,     // 轻微发光
  proceduralTexture: {
    type: 'wood',
    scale: 1.2,
    intensity: 0.6,
    color1: '#8B4513',
    color2: '#D2691E'
  }
};
```

### 发光大理石
```typescript
const glowingMarble = {
  color: '#F0F8FF',
  metalness: 0.3,
  roughness: 0.2,
  opacity: 1.0,
  emissive: '#00BFFF',        // 蓝色发光
  emissiveIntensity: 0.5,
  proceduralTexture: {
    type: 'marble',
    scale: 1.0,
    intensity: 0.4,
    color1: '#F0F8FF',
    color2: '#4682B4'
  }
};
```

## 实际应用场景

### 1. 建筑可视化
- **外墙材质**: 使用石纹纹理模拟真实石材
- **木质装饰**: 使用木纹纹理创建逼真木材效果
- **夜景照明**: 使用发光效果模拟建筑照明

### 2. 产品展示
- **金属产品**: 高金属度 + 低粗糙度 + 轻微发光
- **陶瓷产品**: 低金属度 + 中等粗糙度 + 斑点纹理
- **木制产品**: 零金属度 + 高粗糙度 + 木纹纹理

### 3. 艺术创作
- **抽象效果**: 组合多种纹理和发光效果
- **科幻风格**: 高发光强度 + 金属材质
- **自然风格**: 程序化纹理模拟天然材料

## 性能优化建议

### 1. 纹理使用
- 避免过高的纹理缩放值（建议0.5-3.0范围）
- 合理设置纹理强度（建议0.3-0.8范围）
- 不需要纹理时选择"无纹理"

### 2. 发光效果
- 发光强度不宜过高（建议0-1.5范围）
- 避免过于鲜艳的发光颜色
- 在移动设备上适当降低发光强度

### 3. 材质组合
- 避免同时使用过多复杂效果
- 优先使用预设效果
- 定期清理不需要的材质设置

## 故障排除

### 常见问题
1. **纹理不显示**: 检查纹理类型是否为"none"
2. **发光效果过强**: 降低发光强度值
3. **性能问题**: 减少纹理缩放和复杂度
4. **颜色不匹配**: 检查基础颜色和纹理颜色配置

### 最佳实践
1. 从简单效果开始，逐步添加复杂性
2. 经常保存满意的材质配置
3. 在不同光照条件下测试效果
4. 考虑目标设备的性能限制
