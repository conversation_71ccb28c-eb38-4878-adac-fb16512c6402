import React, { useState } from 'react';
import { <PERSON><PERSON>te, Plus, Palette } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import './custom-material-panel.css';
import { Slider } from '../slider/slider';

interface CustomMaterialPanelProps {
  onChange?: (material: MaterialSettings) => void;
  defaultSettings?: MaterialSettings;
}

export interface MaterialSettings {
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  textureUrl?: string;
  // 发光效果
  emissive: string;
  emissiveIntensity: number;
  // 程序化纹理
  proceduralTexture: {
    type: 'none' | 'spots' | 'wood' | 'stone' | 'marble';
    scale: number;
    intensity: number;
    color1: string;
    color2: string;
  };
}

export const CustomMaterialPanel: React.FC<CustomMaterialPanelProps> = ({
  onChange,
  defaultSettings
}) => {
  // 设置默认值
  const defaultColor = defaultSettings?.color || '#B39B9C';
  const defaultMetalness = defaultSettings?.metalness ?? 0.5;
  const defaultRoughness = defaultSettings?.roughness ?? 0.5;
  const defaultOpacity = defaultSettings?.opacity ?? 1;
  const defaultEmissive = defaultSettings?.emissive || '#000000';
  const defaultEmissiveIntensity = defaultSettings?.emissiveIntensity ?? 0;
  const defaultProceduralTexture = defaultSettings?.proceduralTexture || {
    type: 'none' as const,
    scale: 1,
    intensity: 0.5,
    color1: '#ffffff',
    color2: '#000000'
  };

  // 状态
  const [color, setColor] = useState<string>(defaultColor);
  const [metalness, setMetalness] = useState<number>(defaultMetalness);
  const [roughness, setRoughness] = useState<number>(defaultRoughness);
  const [opacity, setOpacity] = useState<number>(defaultOpacity);
  const [textureUrl, setTextureUrl] = useState<string | undefined>(defaultSettings?.textureUrl);
  const [emissive, setEmissive] = useState<string>(defaultEmissive);
  const [emissiveIntensity, setEmissiveIntensity] = useState<number>(defaultEmissiveIntensity);
  const [proceduralTexture, setProceduralTexture] = useState(defaultProceduralTexture);

  // 发光效果预设
  const emissivePresets = [
    { name: '蓝色发光', color: '#00BFFF', intensity: 0.5 },
    { name: '绿色发光', color: '#00FF7F', intensity: 0.5 },
    { name: '红色发光', color: '#FF4500', intensity: 0.5 },
    { name: '紫色发光', color: '#9370DB', intensity: 0.5 },
    { name: '金色发光', color: '#FFD700', intensity: 0.8 },
    { name: '彩虹发光', color: '#FF1493', intensity: 1.0 },
  ];

  // 更新材质设置
  const updateSettings = (
    newColor?: string,
    newMetalness?: number,
    newRoughness?: number,
    newOpacity?: number,
    newTextureUrl?: string,
    newEmissive?: string,
    newEmissiveIntensity?: number,
    newProceduralTexture?: typeof proceduralTexture
  ) => {
    const updatedSettings: MaterialSettings = {
      color: newColor ?? color,
      metalness: newMetalness ?? metalness,
      roughness: newRoughness ?? roughness,
      opacity: newOpacity ?? opacity,
      textureUrl: newTextureUrl ?? textureUrl,
      emissive: newEmissive ?? emissive,
      emissiveIntensity: newEmissiveIntensity ?? emissiveIntensity,
      proceduralTexture: newProceduralTexture ?? proceduralTexture,
    };

    onChange?.(updatedSettings);
  };

  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    updateSettings(newColor);
  };

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const url = URL.createObjectURL(file);
    setTextureUrl(url);
    updateSettings(undefined, undefined, undefined, undefined, url);
  };

  // 使用吸管工具
  const handleEyeDropper = async () => {
    if ('EyeDropper' in window) {
      try {
        // @ts-expect-error - EyeDropper API 可能不在所有TypeScript类型中
        const eyeDropper = new window.EyeDropper();
        const result = await eyeDropper.open();
        setColor(result.sRGBHex);
        updateSettings(result.sRGBHex);
      } catch (error) {
        console.error('EyeDropper error:', error);
      }
    } else {
      console.warn('EyeDropper API not supported');
    }
  };

  return (
    <div className="custom-material" data-layer="自定义材质">
      <div className="material-property-group column" data-layer="Frame 37">
        <div className="property-label" data-layer="颜色">颜色</div>
        <div className="color-picker-container">
          <HexColorPicker color={color} onChange={handleColorChange} />
          <Pipette 
            className="color-pipette-icon" 
            onClick={handleEyeDropper} 
            data-states="default" 
          />
        </div>
      </div>
      
      <div className="material-property-group" data-layer="Frame 36">
        <div className="property-label" data-layer="金属度">金属度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={metalness}
          onChange={(value) => {
            setMetalness(value);
            updateSettings(undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 34">
        <div className="property-label" data-layer="粗糙度">粗糙度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={roughness}
          onChange={(value) => {
            setRoughness(value);
            updateSettings(undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 35">
        <div className="property-label" data-layer="透明度">透明度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={opacity}
          onChange={(value) => {
            setOpacity(value);
            updateSettings(undefined, undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>

      {/* 发光效果 */}
      <div className="material-property-group" data-layer="Frame 38">
        <div className="property-label" data-layer="发光颜色">发光颜色</div>
        <div className="emissive-color-container">
          <div
            className="emissive-color-preview"
            style={{ backgroundColor: emissive }}
            onClick={() => {
              // 可以在这里添加颜色选择器弹窗
            }}
          />
          <input
            type="color"
            value={emissive}
            onChange={(e) => {
              setEmissive(e.target.value);
              updateSettings(undefined, undefined, undefined, undefined, undefined, e.target.value);
            }}
            className="emissive-color-input"
          />
        </div>
      </div>

      <div className="material-property-group" data-layer="Frame 39">
        <div className="property-label" data-layer="发光强度">发光强度</div>
        <Slider
          min={0}
          max={2}
          step={0.01}
          defaultValue={emissiveIntensity}
          onChange={(value) => {
            setEmissiveIntensity(value);
            updateSettings(undefined, undefined, undefined, undefined, undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>

      {/* 发光效果预设 */}
      <div className="material-property-group column" data-layer="Frame 40">
        <div className="property-label" data-layer="发光预设">发光预设</div>
        <div className="emissive-presets">
          {emissivePresets.map((preset, index) => (
            <div
              key={index}
              className="emissive-preset"
              style={{ backgroundColor: preset.color }}
              title={preset.name}
              onClick={() => {
                setEmissive(preset.color);
                setEmissiveIntensity(preset.intensity);
                updateSettings(undefined, undefined, undefined, undefined, undefined, preset.color, preset.intensity);
              }}
            />
          ))}
        </div>
      </div>
      
      {/* 程序化纹理 */}
      <div className="material-property-group column" data-layer="Frame 40">
        <div className="property-label" data-layer="程序化纹理">程序化纹理</div>
        <select
          className="texture-type-select"
          value={proceduralTexture.type}
          onChange={(e) => {
            const newType = e.target.value as typeof proceduralTexture.type;
            const newProceduralTexture = { ...proceduralTexture, type: newType };
            setProceduralTexture(newProceduralTexture);
            updateSettings(undefined, undefined, undefined, undefined, undefined, undefined, undefined, newProceduralTexture);
          }}
        >
          <option value="none">无纹理</option>
          <option value="spots">斑点纹理</option>
          <option value="wood">木纹纹理</option>
          <option value="stone">石纹纹理</option>
          <option value="marble">大理石纹理</option>
        </select>
      </div>

      {proceduralTexture.type !== 'none' && (
        <>
          <div className="material-property-group" data-layer="Frame 41">
            <div className="property-label" data-layer="纹理缩放">纹理缩放</div>
            <Slider
              min={0.1}
              max={5}
              step={0.1}
              defaultValue={proceduralTexture.scale}
              onChange={(value) => {
                const newProceduralTexture = { ...proceduralTexture, scale: value };
                setProceduralTexture(newProceduralTexture);
                updateSettings(undefined, undefined, undefined, undefined, undefined, undefined, undefined, newProceduralTexture);
              }}
              showValue={false}
              width="100%"
            />
          </div>

          <div className="material-property-group" data-layer="Frame 42">
            <div className="property-label" data-layer="纹理强度">纹理强度</div>
            <Slider
              min={0}
              max={1}
              step={0.01}
              defaultValue={proceduralTexture.intensity}
              onChange={(value) => {
                const newProceduralTexture = { ...proceduralTexture, intensity: value };
                setProceduralTexture(newProceduralTexture);
                updateSettings(undefined, undefined, undefined, undefined, undefined, undefined, undefined, newProceduralTexture);
              }}
              showValue={false}
              width="100%"
            />
          </div>
        </>
      )}

      <div className="material-property-group column" data-layer="Frame 43">
        <div className="property-label" data-layer="纹理贴图">纹理贴图</div>
        <label className="texture-upload-area" data-layer="Frame 32">
          {textureUrl ? (
            <img src={textureUrl} alt="纹理贴图" className="texture-preview" />
          ) : (
            <>
              <div className="plus-icon" data-layer="plus">
                <Plus size={20} color="var(--color-content-regular)" />
              </div>
              <div className="upload-text" data-layer="上传图片">上传图片</div>
            </>
          )}
          <input
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            style={{ display: 'none' }}
          />
        </label>
      </div>
    </div>
  );
};
