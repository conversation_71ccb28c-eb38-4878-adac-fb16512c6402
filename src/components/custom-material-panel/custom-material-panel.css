.custom-material {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.material-property-group {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.material-property-group.column {
  flex-direction: column;
  align-items: flex-start;
}

.material-property-group.column .property-label {
  width: 100%;
  margin-bottom: 4px;
}

.property-label {
  color: var(--color-content-regular);
  font-size: var(--font-size-sm);
  width: 90px;
}

.color-picker-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

/* 自定义react-colorful组件的样式 */
.react-colorful {
  width: 100% !important;
  height: 146px !important;
  border-radius: 8px;
  overflow: hidden;
  outline: 1px var(--color-border) solid;
  outline-offset: -1px;
  margin-bottom: 8px;
}

/* 双色板样式 */
.react-colorful__saturation {
  border-radius: 0;
  border-bottom: none;
}

/* 色相条样式 */
.react-colorful__hue {
  height: 24px;
  border-radius: 0 0 8px 8px;
}

/* 调整选择器指示点样式 */
.react-colorful__pointer {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.40);
  border: 2px solid var(--color-content-invert);
}
/* 吸管图标 */
.color-pipette-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.color-pipette-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.texture-upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 150px;
  background: var(--color-bg-overlay);
  border-radius: var(--radius-m);
  overflow: hidden;
  cursor: pointer;
}

.plus-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  /* 移除宽高和背景，仅用于居中加号图标 */
}

.upload-text {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular, rgba(255, 255, 255, 0.70));
  font-weight: 400;
}

.texture-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 程序化纹理选择器 */
.texture-type-select {
  width: 100%;
  padding: 8px 12px;
  background: var(--color-bg-input);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-s);
  color: var(--color-content-regular);
  font-size: var(--font-size-sm);
  cursor: pointer;
  outline: none;
  transition: border-color 0.2s ease;
}

.texture-type-select:hover {
  border-color: var(--color-border-hover);
}

.texture-type-select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
}

.texture-type-select option {
  background: var(--color-bg-input);
  color: var(--color-content-regular);
}

/* 发光颜色选择器 */
.emissive-color-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.emissive-color-preview {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-s);
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.emissive-color-preview:hover {
  border-color: var(--color-border-hover);
}

.emissive-color-input {
  flex: 1;
  height: 32px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-s);
  background: var(--color-bg-input);
  cursor: pointer;
  outline: none;
}

.emissive-color-input::-webkit-color-swatch-wrapper {
  padding: 0;
}

.emissive-color-input::-webkit-color-swatch {
  border: none;
  border-radius: calc(var(--radius-s) - 1px);
}

/* 发光效果预设 */
.emissive-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.emissive-preset {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-s);
  border: 2px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.emissive-preset:hover {
  border-color: var(--color-primary);
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.emissive-preset:active {
  transform: scale(0.95);
}
