import { ProceduralTextureGenerator } from '../utils/proceduralTextures';
import type { ProceduralTextureConfig } from '../utils/proceduralTextures';

describe('材质增强功能测试', () => {
  describe('程序化纹理生成器', () => {
    const baseConfig: ProceduralTextureConfig = {
      type: 'spots',
      scale: 1,
      intensity: 0.5,
      color1: '#ffffff',
      color2: '#000000'
    };

    test('应该能生成斑点纹理', () => {
      const texture = ProceduralTextureGenerator.generateTexture({
        ...baseConfig,
        type: 'spots'
      });
      
      expect(texture).toBeTruthy();
      expect(texture?.wrapS).toBeDefined();
      expect(texture?.wrapT).toBeDefined();
    });

    test('应该能生成木纹纹理', () => {
      const texture = ProceduralTextureGenerator.generateTexture({
        ...baseConfig,
        type: 'wood'
      });
      
      expect(texture).toBeTruthy();
    });

    test('应该能生成石纹纹理', () => {
      const texture = ProceduralTextureGenerator.generateTexture({
        ...baseConfig,
        type: 'stone'
      });
      
      expect(texture).toBeTruthy();
    });

    test('应该能生成大理石纹理', () => {
      const texture = ProceduralTextureGenerator.generateTexture({
        ...baseConfig,
        type: 'marble'
      });
      
      expect(texture).toBeTruthy();
    });

    test('无纹理类型应该返回null', () => {
      const texture = ProceduralTextureGenerator.generateTexture({
        ...baseConfig,
        type: 'none'
      });
      
      expect(texture).toBeNull();
    });

    test('应该正确设置纹理重复模式', () => {
      const texture = ProceduralTextureGenerator.generateTexture({
        ...baseConfig,
        scale: 2
      });
      
      expect(texture?.repeat.x).toBe(2);
      expect(texture?.repeat.y).toBe(2);
    });
  });

  describe('材质设置接口', () => {
    test('MaterialSettings接口应该包含所有必需属性', () => {
      const materialSettings = {
        color: '#ff0000',
        metalness: 0.5,
        roughness: 0.3,
        opacity: 1.0,
        emissive: '#00ff00',
        emissiveIntensity: 0.8,
        proceduralTexture: {
          type: 'wood' as const,
          scale: 1.5,
          intensity: 0.7,
          color1: '#8B4513',
          color2: '#D2691E'
        }
      };

      // 验证所有属性都存在
      expect(materialSettings.color).toBeDefined();
      expect(materialSettings.metalness).toBeDefined();
      expect(materialSettings.roughness).toBeDefined();
      expect(materialSettings.opacity).toBeDefined();
      expect(materialSettings.emissive).toBeDefined();
      expect(materialSettings.emissiveIntensity).toBeDefined();
      expect(materialSettings.proceduralTexture).toBeDefined();
      expect(materialSettings.proceduralTexture.type).toBe('wood');
    });
  });
});

// 集成测试 - 需要DOM环境
describe('材质面板集成测试', () => {
  // 模拟Canvas环境
  beforeAll(() => {
    // 创建模拟的Canvas和Context
    const mockCanvas = {
      width: 512,
      height: 512,
      getContext: jest.fn(() => ({
        fillStyle: '',
        strokeStyle: '',
        globalAlpha: 1,
        lineWidth: 1,
        fillRect: jest.fn(),
        beginPath: jest.fn(),
        arc: jest.fn(),
        fill: jest.fn(),
        stroke: jest.fn(),
        moveTo: jest.fn(),
        lineTo: jest.fn(),
        quadraticCurveTo: jest.fn(),
        closePath: jest.fn(),
        createRadialGradient: jest.fn(() => ({
          addColorStop: jest.fn()
        })),
        createLinearGradient: jest.fn(() => ({
          addColorStop: jest.fn()
        })),
        getImageData: jest.fn(() => ({
          data: new Uint8ClampedArray(512 * 512 * 4)
        })),
        putImageData: jest.fn()
      }))
    };

    // 模拟document.createElement
    global.document = {
      createElement: jest.fn(() => mockCanvas)
    } as any;
  });

  test('程序化纹理生成器应该能在模拟环境中工作', () => {
    const config: ProceduralTextureConfig = {
      type: 'spots',
      scale: 1,
      intensity: 0.5,
      color1: '#ffffff',
      color2: '#000000'
    };

    expect(() => {
      ProceduralTextureGenerator.generateTexture(config);
    }).not.toThrow();
  });
});
