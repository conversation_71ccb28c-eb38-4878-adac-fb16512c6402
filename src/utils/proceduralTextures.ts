import * as THREE from 'three';

export type TextureType = 'none' | 'spots' | 'wood' | 'stone' | 'marble';

export interface ProceduralTextureConfig {
  type: TextureType;
  scale: number;
  intensity: number;
  color1: string;
  color2: string;
}

/**
 * 生成程序化纹理
 */
export class ProceduralTextureGenerator {
  private static canvas: HTMLCanvasElement;
  private static ctx: CanvasRenderingContext2D;
  
  private static getCanvas(): { canvas: HTMLCanvasElement; ctx: CanvasRenderingContext2D } {
    if (!this.canvas) {
      this.canvas = document.createElement('canvas');
      this.canvas.width = 512;
      this.canvas.height = 512;
      this.ctx = this.canvas.getContext('2d')!;
    }
    return { canvas: this.canvas, ctx: this.ctx };
  }

  /**
   * 生成斑点纹理
   */
  private static generateSpotsTexture(config: ProceduralTextureConfig): THREE.Texture {
    const { canvas, ctx } = this.getCanvas();
    const { scale, intensity, color1, color2 } = config;

    // 清空画布
    ctx.fillStyle = color1;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 生成随机斑点
    const spotCount = Math.floor(150 * scale);

    for (let i = 0; i < spotCount; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const radius = Math.random() * 15 * scale + 3;

      // 创建径向渐变以获得更自然的斑点效果
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
      gradient.addColorStop(0, color2);
      gradient.addColorStop(1, color1);

      ctx.fillStyle = gradient;
      ctx.globalAlpha = intensity * (0.3 + Math.random() * 0.7);
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fill();
    }

    ctx.globalAlpha = 1;

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(scale, scale);

    return texture;
  }

  /**
   * 生成木纹纹理
   */
  private static generateWoodTexture(config: ProceduralTextureConfig): THREE.Texture {
    const { canvas, ctx } = this.getCanvas();
    const { scale, intensity, color1, color2 } = config;

    // 基础颜色
    ctx.fillStyle = color1;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 生成年轮效果
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const maxRadius = Math.max(canvas.width, canvas.height);

    for (let radius = 10; radius < maxRadius; radius += 15 / scale) {
      ctx.strokeStyle = color2;
      ctx.globalAlpha = intensity * (0.3 + Math.random() * 0.4);
      ctx.lineWidth = 1 + Math.random() * 2;

      ctx.beginPath();
      for (let angle = 0; angle < Math.PI * 2; angle += 0.1) {
        // 添加随机扰动创建自然的年轮效果
        const distortion = Math.sin(angle * 8) * 3 + Math.random() * 5;
        const x = centerX + Math.cos(angle) * (radius + distortion);
        const y = centerY + Math.sin(angle) * (radius + distortion);

        if (angle === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.closePath();
      ctx.stroke();
    }

    // 添加木纹纹理
    ctx.globalAlpha = intensity * 0.5;
    for (let i = 0; i < 50 * scale; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const length = 20 + Math.random() * 40;
      const angle = Math.random() * Math.PI * 2;

      ctx.strokeStyle = color2;
      ctx.lineWidth = 0.5 + Math.random();
      ctx.beginPath();
      ctx.moveTo(x, y);
      ctx.lineTo(x + Math.cos(angle) * length, y + Math.sin(angle) * length);
      ctx.stroke();
    }

    ctx.globalAlpha = 1;

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(scale, scale);

    return texture;
  }

  /**
   * 生成石纹纹理
   */
  private static generateStoneTexture(config: ProceduralTextureConfig): THREE.Texture {
    const { canvas, ctx } = this.getCanvas();
    const { scale, intensity, color1, color2 } = config;
    
    // 基础颜色
    ctx.fillStyle = color1;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 生成随机石纹
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    const color1RGB = this.hexToRgb(color1);
    const color2RGB = this.hexToRgb(color2);
    
    for (let i = 0; i < data.length; i += 4) {
      const noise = Math.random() * intensity;
      
      data[i] = Math.floor(color1RGB.r + (color2RGB.r - color1RGB.r) * noise);     // R
      data[i + 1] = Math.floor(color1RGB.g + (color2RGB.g - color1RGB.g) * noise); // G
      data[i + 2] = Math.floor(color1RGB.b + (color2RGB.b - color1RGB.b) * noise); // B
      data[i + 3] = 255; // A
    }
    
    ctx.putImageData(imageData, 0, 0);
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(scale, scale);
    
    return texture;
  }

  /**
   * 生成大理石纹理
   */
  private static generateMarbleTexture(config: ProceduralTextureConfig): THREE.Texture {
    const { canvas, ctx } = this.getCanvas();
    const { scale, intensity, color1, color2 } = config;
    
    // 基础颜色
    ctx.fillStyle = color1;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 生成大理石纹理
    ctx.strokeStyle = color2;
    ctx.globalAlpha = intensity;
    ctx.lineWidth = 3;
    
    for (let i = 0; i < 20 * scale; i++) {
      ctx.beginPath();
      
      const startX = Math.random() * canvas.width;
      const startY = Math.random() * canvas.height;
      
      ctx.moveTo(startX, startY);
      
      // 创建曲线
      for (let j = 0; j < 10; j++) {
        const x = startX + Math.random() * 100 - 50;
        const y = startY + j * 20 + Math.random() * 40 - 20;
        const cpX = startX + Math.random() * 50 - 25;
        const cpY = startY + j * 10 + Math.random() * 20 - 10;
        
        ctx.quadraticCurveTo(cpX, cpY, x, y);
      }
      
      ctx.stroke();
    }
    
    ctx.globalAlpha = 1;
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(scale, scale);
    
    return texture;
  }

  /**
   * 将十六进制颜色转换为RGB
   */
  private static hexToRgb(hex: string): { r: number; g: number; b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  }

  /**
   * 生成程序化纹理
   */
  public static generateTexture(config: ProceduralTextureConfig): THREE.Texture | null {
    switch (config.type) {
      case 'spots':
        return this.generateSpotsTexture(config);
      case 'wood':
        return this.generateWoodTexture(config);
      case 'stone':
        return this.generateStoneTexture(config);
      case 'marble':
        return this.generateMarbleTexture(config);
      case 'none':
      default:
        return null;
    }
  }
}
