# 材质调节功能优化说明

## 新增功能概览

本次优化为材质调节系统添加了以下高级功能：

### 1. 发光效果 (Emissive)
- **发光颜色选择**: 支持自定义发光颜色
- **发光强度调节**: 0-2范围的强度控制
- **发光预设**: 6种预设发光效果
  - 蓝色发光 (#00BFFF)
  - 绿色发光 (#00FF7F) 
  - 红色发光 (#FF4500)
  - 紫色发光 (#9370DB)
  - 金色发光 (#FFD700)
  - 彩虹发光 (#FF1493)

### 2. 程序化纹理生成
支持5种程序化纹理类型：

#### 斑点纹理 (Spots)
- 生成随机分布的圆形斑点
- 使用径向渐变创建自然过渡效果
- 可调节斑点密度和透明度

#### 木纹纹理 (Wood)
- 模拟真实木材的年轮结构
- 添加随机纹理线条增强真实感
- 支持颜色渐变和扰动效果

#### 石纹纹理 (Stone)
- 基于噪声算法生成石材质感
- 随机颜色混合模拟天然石材
- 可调节纹理粗糙度

#### 大理石纹理 (Marble)
- 生成流动的大理石纹路
- 使用曲线算法创建自然纹理
- 支持多层纹理叠加

#### 无纹理 (None)
- 关闭程序化纹理
- 保持材质原始状态

### 3. 纹理参数控制
- **纹理缩放**: 0.1-5倍缩放范围
- **纹理强度**: 0-1透明度控制
- **颜色配置**: 双色配置支持渐变效果

## 技术实现

### 数据结构扩展
```typescript
interface MaterialSettings {
  // 原有属性
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  textureUrl?: string;
  
  // 新增发光属性
  emissive: string;
  emissiveIntensity: number;
  
  // 新增程序化纹理属性
  proceduralTexture: {
    type: 'none' | 'spots' | 'wood' | 'stone' | 'marble';
    scale: number;
    intensity: number;
    color1: string;
    color2: string;
  };
}
```

### 核心组件

#### ProceduralTextureGenerator
- 位置: `src/utils/proceduralTextures.ts`
- 功能: 生成各种程序化纹理
- 使用Canvas 2D API创建纹理图案
- 输出Three.js兼容的CanvasTexture

#### CustomMaterialPanel
- 位置: `src/components/custom-material-panel/`
- 新增发光颜色选择器和预设
- 新增程序化纹理类型选择
- 新增纹理参数调节滑块

### Three.js材质应用
在RenderPage.tsx中扩展了材质应用逻辑：

```typescript
// 发光效果
if (mat.emissive) {
  materialConfig.emissive = new THREE.Color(mat.emissive);
  materialConfig.emissiveIntensity = mat.emissiveIntensity || 0;
}

// 程序化纹理
if (mat.proceduralTexture && mat.proceduralTexture.type !== 'none') {
  const proceduralTexture = ProceduralTextureGenerator.generateTexture(mat.proceduralTexture);
  if (proceduralTexture) {
    materialConfig.map = proceduralTexture;
  }
}
```

## 用户界面优化

### 发光效果控制
- 紧凑的颜色选择器设计
- 直观的预设色块选择
- 实时预览效果

### 程序化纹理控制
- 下拉菜单选择纹理类型
- 条件显示参数控制
- 实时纹理生成和应用

### 响应式设计
- 适配不同屏幕尺寸
- 保持一致的视觉风格
- 流畅的交互体验

## 性能优化

### 纹理缓存
- 程序化纹理按需生成
- 避免重复计算相同参数的纹理
- 内存使用优化

### 渲染优化
- 只在参数变化时重新生成纹理
- 使用Three.js的纹理重用机制
- 避免不必要的材质重建

## 兼容性

### 向后兼容
- 保持原有材质系统完整性
- 新功能为可选增强
- 不影响现有材质数据

### 浏览器支持
- 支持现代浏览器的Canvas 2D API
- 兼容WebGL渲染管线
- 优雅降级处理

## 使用指南

### 基础使用
1. 选择可编辑材质
2. 切换到"自定义"标签页
3. 调节各项材质参数
4. 实时预览效果

### 发光效果
1. 选择发光颜色
2. 调节发光强度
3. 或直接选择预设效果

### 程序化纹理
1. 选择纹理类型
2. 调节缩放和强度
3. 配置纹理颜色
4. 实时查看效果

## 未来扩展

### 计划功能
- 更多程序化纹理类型
- 纹理混合模式
- 动画纹理效果
- 材质预设保存

### 技术改进
- GPU加速纹理生成
- 更高分辨率纹理支持
- 实时纹理编辑器
- 材质导入导出功能
